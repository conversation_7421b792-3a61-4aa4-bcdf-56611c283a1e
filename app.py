import os, base64, io, json
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from dotenv import load_dotenv
from azure.cosmos import CosmosClient
from PyPDF2 import PdfReader
import docx
from pptx import Presentation

# 🔹 Load env vars
load_dotenv()
COSMOS_ENDPOINT = os.getenv("COSMOS_URL")
COSMOS_KEY = os.getenv("COSMOS_KEY")
DATABASE_NAME = os.getenv("DATABASE_NAME")
CONTAINER_NAME = os.getenv("CONTAINER_NAME")

# 🔹 Cosmos client
cosmos_client = CosmosClient(COSMOS_ENDPOINT, COSMOS_KEY)
db = cosmos_client.create_database_if_not_exists(id=DATABASE_NAME)
container = db.create_container_if_not_exists(id=CONTAINER_NAME, partition_key="/id")

# 🔹 FastAPI app
app = FastAPI()

class CopilotRequest(BaseModel):
    copilot_url: str

# ---------- File extractors ----------
def extract_text_from_pdf(data: bytes) -> str:
    reader = PdfReader(io.BytesIO(data))
    return "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])

def extract_text_from_docx(data: bytes) -> str:
    document = docx.Document(io.BytesIO(data))
    return "\n".join([para.text for para in document.paragraphs])

def extract_text_from_pptx(data: bytes) -> str:
    prs = Presentation(io.BytesIO(data))
    texts = []
    for slide in prs.slides:
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                texts.append(shape.text)
    return "\n".join(texts)

def extract_text_from_txt(data: bytes) -> str:
    return data.decode("utf-8", errors="ignore")

def extract_text_from_json(data: bytes) -> str:
    return json.dumps(json.loads(data.decode("utf-8")), indent=2)

# ---------- Main processor ----------
def extract_text_from_file(data: bytes, filename: str) -> str:
    ext = filename.lower().split(".")[-1]
    if ext == "pdf": return extract_text_from_pdf(data)
    if ext == "docx": return extract_text_from_docx(data)
    if ext == "pptx": return extract_text_from_pptx(data)
    if ext == "txt": return extract_text_from_txt(data)
    if ext == "json": return extract_text_from_json(data)
    return "<Unsupported file type>"

# ---------- API endpoint ----------
@app.post("/process-from-copilot")
async def process_from_copilot(request: CopilotRequest):
    import requests
    try:
        res = requests.get(request.copilot_url)
        res.raise_for_status()
        copilot_data = res.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to fetch Copilot data: {e}")

    base64_str = copilot_data.get("file_base64")
    filename = copilot_data.get("filename", "uploaded.bin")

    if not base64_str:
        raise HTTPException(status_code=400, detail="Missing base64 data from Copilot")

    try:
        file_bytes = base64.b64decode(base64_str)
        extracted_text = extract_text_from_file(file_bytes, filename)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Base64 decoding failed: {e}")

    doc_id = filename + "_0"
    container.upsert_item({
        "id": doc_id,
        "filename": filename,
        "content": extracted_text
    })

    return {"status": "success", "filename": filename, "cosmos_id": doc_id}
